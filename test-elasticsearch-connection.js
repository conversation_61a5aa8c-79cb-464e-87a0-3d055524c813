/**
 * 测试ElasticSearch连接和摘要功能
 */

// 测试代理服务器连接
async function testProxyConnection() {
  try {
    console.log('🔍 测试代理服务器连接...');
    
    const response = await fetch('http://localhost:9202/_cluster/health');
    const data = await response.json();
    
    console.log('✅ 代理服务器连接成功!');
    console.log('📊 集群状态:', data.status);
    console.log('🔢 节点数量:', data.number_of_nodes);
    
    return true;
  } catch (error) {
    console.error('❌ 代理服务器连接失败:', error.message);
    return false;
  }
}

// 测试创建索引
async function testCreateIndex() {
  try {
    console.log('🔍 测试创建prompts索引...');
    
    const mapping = {
      mappings: {
        properties: {
          id: { type: 'keyword' },
          content: { 
            type: 'text',
            analyzer: 'standard',
            fields: {
              keyword: { type: 'keyword' }
            }
          },
          type: { type: 'keyword' },
          score: { type: 'float' },
          summary: { type: 'text' },
          timestamp: { type: 'date' },
          metadata: {
            properties: {
              tokenCount: { type: 'integer' },
              language: { type: 'keyword' }
            }
          }
        }
      }
    };
    
    const response = await fetch('http://localhost:9202/prompts', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(mapping)
    });
    
    if (response.ok) {
      console.log('✅ prompts索引创建成功!');
      return true;
    } else {
      const error = await response.text();
      console.log('⚠️ 索引可能已存在或创建失败:', error);
      return false;
    }
  } catch (error) {
    console.error('❌ 创建索引失败:', error.message);
    return false;
  }
}

// 测试保存文档
async function testSaveDocument() {
  try {
    console.log('🔍 测试保存摘要文档...');
    
    const testDoc = {
      id: 'test-summary-' + Date.now(),
      content: '这是一个测试的增强提示词内容',
      type: 'enhanced',
      summary: '原始提示词摘要：这是一个测试摘要\n\n增强提示词摘要：这是增强后的摘要',
      timestamp: new Date().toISOString(),
      metadata: {
        tokenCount: 50,
        language: 'zh-CN'
      }
    };
    
    const response = await fetch(`http://localhost:9202/prompts/_doc/${testDoc.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testDoc)
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ 摘要文档保存成功!');
      console.log('📄 文档ID:', result._id);
      return true;
    } else {
      const error = await response.text();
      console.error('❌ 保存文档失败:', error);
      return false;
    }
  } catch (error) {
    console.error('❌ 保存文档失败:', error.message);
    return false;
  }
}

// 运行所有测试
async function runTests() {
  console.log('🚀 开始ElasticSearch连接测试...\n');
  
  const proxyOk = await testProxyConnection();
  console.log('');
  
  if (proxyOk) {
    const indexOk = await testCreateIndex();
    console.log('');
    
    const saveOk = await testSaveDocument();
    console.log('');
    
    if (proxyOk && saveOk) {
      console.log('🎉 所有测试通过! ElasticSearch连接正常，摘要功能可以使用。');
    } else {
      console.log('⚠️ 部分测试失败，但基本连接正常。');
    }
  } else {
    console.log('❌ 代理服务器连接失败，请检查服务是否启动。');
  }
}

// 如果直接运行此脚本
if (typeof window === 'undefined') {
  runTests();
}

// 导出测试函数供浏览器使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testProxyConnection, testCreateIndex, testSaveDocument, runTests };
}
