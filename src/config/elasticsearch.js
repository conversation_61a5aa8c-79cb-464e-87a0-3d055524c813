/**
 * ElasticSearch配置文件
 */

export const elasticsearchConfig = {
  // ElasticSearch服务器配置 (通过代理访问)
  node: process.env.REACT_APP_ELASTICSEARCH_URL || 'http://localhost:9202',
  
  // 认证配置（如果需要）
  auth: {
    username: process.env.REACT_APP_ELASTICSEARCH_USERNAME || '',
    password: process.env.REACT_APP_ELASTICSEARCH_PASSWORD || ''
  },
  
  // 索引配置
  indices: {
    prompts: 'prompts',
    evaluations: 'evaluations'
  },
  
  // 搜索配置
  search: {
    defaultSize: 10,
    maxSize: 100
  },
  
  // 连接配置
  connection: {
    requestTimeout: 30000,
    pingTimeout: 3000,
    maxRetries: 3
  }
};

export default elasticsearchConfig;
