/**
 * Token Utilities - Shared token counting functionality
 * Provides both simple estimation and advanced token counting
 */

import TokenCounter from './tokenCounter.js';

/**
 * Simple token estimation used across services
 * This is a lightweight estimation method that doesn't require external dependencies
 * @param {string} text - Text content to estimate tokens for
 * @returns {number} - Estimated token count
 */
export const estimateTokenCount = (text) => {
  if (!text) return 0;
  
  // Simple token estimation: Chinese characters + English words
  // This matches the implementation used in multiple services
  const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
  const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
  return chineseChars + englishWords;
};

/**
 * Advanced token counting using tiktoken (when available)
 * Falls back to approximation if tiktoken is not available
 * @param {string} text - Text content to count tokens for
 * @param {string} model - Model name for token counting (default: gpt-3.5-turbo)
 * @returns {Promise<number>} - Accurate token count
 */
export const countTokens = async (text, model = "gpt-3.5-turbo") => {
  try {
    const counter = new TokenCounter(model);
    return await counter.countTokens(text);
  } catch (error) {
    console.warn('Advanced token counting failed, falling back to estimation:', error);
    return estimateTokenCount(text);
  }
};

/**
 * Get token count with metadata
 * @param {string} text - Text content to analyze
 * @param {string} model - Model name for token counting
 * @returns {Promise<Object>} - Token count with metadata
 */
export const getTokenAnalysis = async (text, model = "gpt-3.5-turbo") => {
  const estimatedCount = estimateTokenCount(text);
  const accurateCount = await countTokens(text, model);
  
  return {
    estimated: estimatedCount,
    accurate: accurateCount,
    difference: Math.abs(accurateCount - estimatedCount),
    method: 'tiktoken',
    model: model,
    textLength: text.length
  };
};

export default {
  estimateTokenCount,
  countTokens,
  getTokenAnalysis
};
