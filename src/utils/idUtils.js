/**
 * ID Generation Utilities - Shared ID generation functionality
 * Provides consistent ID generation methods across services
 */

/**
 * Generate a random string component for IDs
 * @param {number} length - Length of the random string (default: 9)
 * @returns {string} - Random alphanumeric string
 */
const generateRandomString = (length = 9) => {
  return Math.random().toString(36).substring(2, 2 + length);
};

/**
 * Generate a timestamp-based ID with prefix
 * @param {string} prefix - Prefix for the ID
 * @param {number} randomLength - Length of random component (default: 9)
 * @returns {string} - Generated ID
 */
const generateTimestampId = (prefix, randomLength = 9) => {
  const timestamp = Date.now();
  const randomPart = generateRandomString(randomLength);
  return `${prefix}_${timestamp}_${randomPart}`;
};

/**
 * Generate a summary ID (used by promptSummaryService)
 * @returns {string} - Summary ID in format: summary_timestamp_random
 */
export const generateSummaryId = () => {
  return generateTimestampId('summary');
};

/**
 * Generate a workflow ID (used by promptEvaluationService)
 * @returns {string} - Workflow ID in format: workflow_timestamp_random
 */
export const generateWorkflowId = () => {
  return generateTimestampId('workflow');
};

/**
 * Generate a database ID (used by promptEvaluationService)
 * @returns {string} - Database ID in format: db_timestamp_random
 */
export const generateDatabaseId = () => {
  return generateTimestampId('db');
};

/**
 * Generate a generic ID with custom prefix
 * @param {string} prefix - Custom prefix for the ID
 * @param {number} randomLength - Length of random component (default: 9)
 * @returns {string} - Generated ID
 */
export const generateId = (prefix = 'id', randomLength = 9) => {
  return generateTimestampId(prefix, randomLength);
};

/**
 * Generate a UUID-like ID (without external dependencies)
 * @returns {string} - UUID-like string
 */
export const generateUUID = () => {
  // Simple UUID v4-like generation without crypto dependencies
  const chars = '0123456789abcdef';
  let result = '';
  for (let i = 0; i < 32; i++) {
    if (i === 8 || i === 12 || i === 16 || i === 20) {
      result += '-';
    }
    result += chars[Math.floor(Math.random() * 16)];
  }
  return result;
};

/**
 * Generate an evaluation ID
 * @returns {string} - Evaluation ID
 */
export const generateEvaluationId = () => {
  return generateTimestampId('eval');
};

/**
 * Generate a prompt ID
 * @returns {string} - Prompt ID
 */
export const generatePromptId = () => {
  return generateTimestampId('prompt');
};

export default {
  generateSummaryId,
  generateWorkflowId,
  generateDatabaseId,
  generateId,
  generateUUID,
  generateEvaluationId,
  generatePromptId,
  generateRandomString
};
