import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { FaCopy, FaRobot, FaExternalLinkAlt, FaTimes, FaExpandAlt, FaCompressAlt, FaMagic, FaCheck, FaTimes as FaReject, FaExchangeAlt, FaEdit } from 'react-icons/fa';
import { promptManager } from '../prompts';
import { promptSummaryService } from '../services/promptSummaryService';

const OutputContainer = styled.div`
  flex: 1;
  background-color: ${props => props.theme.surface};
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
`;

const OutputHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid ${props => props.theme.border};
`;

const OutputTitle = styled.h3`
  font-size: 16px;
  font-weight: 500;
  color: ${props => props.theme.text};
  margin: 0;
`;

const ButtonsGroup = styled.div`
  display: flex;
  gap: 8px;
`;

const ActionButton = styled.button`
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: ${props => props.primary ? props.theme.primary : props.theme.borderLight};
  color: ${props => props.primary ? '#fff' : props.theme.textSecondary};
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;

  &:hover {
    background-color: ${props => props.primary ? props.theme.primaryDark : props.theme.border};
    color: ${props => props.primary ? '#fff' : props.theme.text};
  }
`;

const TreeDisplay = styled.pre`
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  color: ${props => props.theme.textSecondary};
  background-color: ${props => props.theme.background};
  font-family: 'Courier New', monospace;
  font-size: 14px;
  white-space: pre;
  line-height: 1.6;
  tab-size: 4;
  width: 100%;
  min-width: 500px;
  word-break: keep-all;
  word-wrap: normal;
  user-select: text;
`;

const NotificationMessage = styled.div`
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 16px;
  background-color: #38a169;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
  opacity: ${props => props.show ? 1 : 0};
  transform: translateY(${props => props.show ? 0 : '20px'});
  transition: opacity 0.3s, transform 0.3s;
  z-index: 1000;
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  width: 80%;
  max-width: 900px;
  min-width: 500px;
  height: 70vh;
  min-height: 400px;
  background-color: ${props => props.theme.surface};
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  resize: both;
  overflow: auto;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    right: 4px;
    bottom: 4px;
    width: 10px;
    height: 10px;
    border-right: 2px solid ${props => props.theme.border};
    border-bottom: 2px solid ${props => props.theme.border};
    cursor: nwse-resize;
  }
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-shrink: 0;
`;

const ModalTitle = styled.h3`
  margin: 0;
  color: ${props => props.theme.text};
  flex-shrink: 0;
`;

const ModalActions = styled.div`
  display: flex;
  gap: 8px;
`;

const IconButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  
  &:hover {
    background-color: #edf2f7;
    color: #2d3748;
  }
`;

const ModalBody = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 300px;
  height: 100%;
  padding: 12px;
  background-color: ${props => props.theme.background};
  border: 1px solid ${props => props.theme.border};
  border-radius: 4px;
  color: ${props => props.theme.text};
  font-size: 14px;
  font-family: monospace;
  line-height: 1.5;
  resize: none;
  outline: none;
  flex: 1;
  &:focus {
    border-color: ${props => props.theme.primary};
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
  flex-shrink: 0;
`;

const Button = styled.button`
  padding: 8px 16px;
  background-color: ${props => props.primary ? '#3182ce' : 'transparent'};
  color: ${props => props.primary ? '#fff' : '#4a5568'};
  border: ${props => props.primary ? 'none' : '1px solid #e2e8f0'};
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  &:hover {
    background-color: ${props => props.primary ? '#2c5282' : '#edf2f7'};
    color: ${props => props.primary ? '#fff' : '#2d3748'};
  }
`;

const LoadingIndicator = styled.div`
  display: inline-block;
  width: ${props => props.size || '20px'};
  height: ${props => props.size || '20px'};
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
`;

// 对比视图相关的样式组件
const ComparisonContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
`;

const ComparisonHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #edf2f7;
  border-bottom: 1px solid #e2e8f0;
`;

const ComparisonTitle = styled.div`
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const InfoTooltip = styled.div`
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #3182ce;
  color: white;
  font-size: 12px;
  font-weight: bold;
  cursor: help;
  
  &:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 8px;
    padding: 8px 12px;
    background-color: #2d3748;
    color: white;
    border-radius: 4px;
    font-size: 12px;
    font-weight: normal;
    white-space: pre-wrap;
    width: 250px;
    z-index: 1000;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
`;

const HighlightedChanges = styled.div`
  margin-top: 8px;
  padding: 6px 10px;
  background-color: ${props => props.type === 'added' ? '#e6ffec' : props.type === 'removed' ? '#ffebe9' : '#f8f9fa'};
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  color: ${props => props.type === 'added' ? '#22863a' : props.type === 'removed' ? '#cb2431' : '#4a5568'};
  border-left: 3px solid ${props => props.type === 'added' ? '#22863a' : props.type === 'removed' ? '#cb2431' : '#e2e8f0'};
  display: ${props => props.visible ? 'block' : 'none'};
`;

const EnhancementInfo = styled.div`
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #ebf8ff;
  border-radius: 4px;
  font-size: 12px;
  color: #2b6cb0;
  display: flex;
  flex-direction: column;
  gap: 6px;
`;

const EnhancementPoint = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 6px;
  
  &:before {
    content: "•";
    color: #3182ce;
    font-weight: bold;
  }
`;

// 添加可拖动分隔线组件
const DraggableDivider = styled.div`
  width: 5px;
  background-color: #e2e8f0;
  cursor: col-resize;
  transition: background-color 0.2s;
  
  &:hover, &:active {
    background-color: #3182ce;
  }
`;

const ComparisonBody = styled.div`
  display: flex;
  flex: 1;
  min-height: 0;
  overflow: hidden;
  position: relative;
`;

const PromptPanel = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: ${props => props.showBorder ? '1px solid #e2e8f0' : 'none'};
  height: 100%;
  overflow: hidden;
`;

const PanelHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: ${props => props.active ? '#ebf8ff' : '#f7fafc'};
  border-bottom: 1px solid #e2e8f0;
`;

const PanelTitleGroup = styled.div`
  display: flex;
  align-items: center;
  gap: 6px;
`;

const PanelTitle = styled.div`
  font-size: 13px;
  font-weight: ${props => props.active ? '600' : '500'};
  color: ${props => props.active ? '#3182ce' : '#4a5568'};
  display: flex;
  align-items: center;
  gap: 6px;
`;

const PanelActions = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
`;

const PanelButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: none;
  background-color: transparent;
  color: #718096;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    background-color: #edf2f7;
    color: #4a5568;
  }
  
  &:focus {
    outline: none;
  }
`;

const StatusBadge = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: ${props => {
    if (props.success) return '#c6f6d5';
    if (props.loading) return '#fefcbf';
    return '#edf2f7';
  }};
  color: ${props => {
    if (props.success) return '#38a169';
    if (props.loading) return '#d69e2e';
    return '#718096';
  }};
`;

const SmallLoadingIndicator = styled(LoadingIndicator)`
  width: 12px;
  height: 12px;
  border-width: 2px;
  margin-right: 4px;
`;

const PanelContent = styled.div`
  flex: 1;
  overflow-y: auto;
  position: relative;
`;

const Editor = styled.textarea`
  width: 100%;
  height: 100%;
  padding: 12px;
  border: none;
  resize: none;
  font-size: 14px;
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.5;
  color: #2d3748;
  background-color: ${props => props.readOnly ? '#f7fafc' : '#fff'};
  &:focus {
    outline: none;
  }
`;

const ComparisonFooter = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #f7fafc;
  border-top: 1px solid #e2e8f0;
`;

const FooterActions = styled.div`
  display: flex;
  gap: 12px;
`;

const ComparisonButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  
  background-color: ${props => {
    if (props.primary) return '#3182ce';
    if (props.success) return '#38a169';
    if (props.danger) return '#e53e3e';
    return '#edf2f7';
  }};
  
  color: ${props => {
    if (props.primary || props.success || props.danger) return '#fff';
    return '#4a5568';
  }};
  
  &:hover {
    background-color: ${props => {
      if (props.primary) return '#2c5282';
      if (props.success) return '#2f855a';
      if (props.danger) return '#c53030';
      return '#e2e8f0';
    }};
  }
  
  &:disabled {
    background-color: #a0aec0;
    cursor: not-allowed;
  }
`;

const StreamProgressIndicator = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  width: ${props => props.progress}%;
  height: 3px;
  background-color: #3182ce;
  transition: width 0.3s ease;
`;

// 摘要Modal样式组件
const SummaryModal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
`;

const SummaryModalContent = styled.div`
  width: 90%;
  max-width: 700px;
  max-height: 80vh;
  background-color: ${props => props.theme.surface};
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const SummaryModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid ${props => props.theme.border};
`;

const SummaryModalTitle = styled.h3`
  margin: 0;
  color: ${props => props.theme.text};
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
`;

const SummaryModalBody = styled.div`
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: ${props => props.theme.background};
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: ${props => props.theme.border};
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: ${props => props.theme.textSecondary};
  }
`;

const SummaryContent = styled.div`
  background-color: ${props => props.theme.background};
  border: 1px solid ${props => props.theme.border};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  line-height: 1.6;
  color: ${props => props.theme.text};
  white-space: pre-wrap;
  word-wrap: break-word;
`;

const SummaryLabel = styled.div`
  font-weight: 600;
  color: ${props => props.theme.primary};
  margin-bottom: 8px;
  font-size: 14px;
`;

const SummaryModalFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid ${props => props.theme.border};
`;

const SummaryCloseButton = styled.button`
  background-color: ${props => props.theme.primary};
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;

  &:hover {
    background-color: ${props => props.theme.primaryDark || '#2c5282'};
  }
`;

const TreeOutput = ({ treeData, darkMode }) => {
  const [asciiTree, setAsciiTree] = useState('');
  const [showNotification, setShowNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [promptText, setPromptText] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancementAvailable, setEnhancementAvailable] = useState(true);
  
  // 对比视图相关状态
  const [showComparison, setShowComparison] = useState(false);
  const [originalPrompt, setOriginalPrompt] = useState('');
  const [enhancedPrompt, setEnhancedPrompt] = useState('');
  const [streamProgress, setStreamProgress] = useState(0);
  const [activePanel, setActivePanel] = useState('original');
  
  // 添加以下状态和引用
  const [dividerPosition, setDividerPosition] = useState(50); // 百分比
  const [isDragging, setIsDragging] = useState(false);
  const leftPanelRef = React.useRef(null);
  const rightPanelRef = React.useRef(null);
  const comparisonBodyRef = React.useRef(null);

  // 摘要展示Modal相关状态
  const [showSummaryModal, setShowSummaryModal] = useState(false);
  const [summaryData, setSummaryData] = useState(null);

  useEffect(() => {
    const generateAsciiTree = (node, prefix = '', isLast = true, rootPath = '') => {
      // 检查节点是否存在
      if (!node || !node.name) {
        return '';
      }
      
      const path = rootPath ? `${rootPath}/${node.name}` : node.name;
      
      let result = prefix;
      
      // Add the appropriate connector
      if (prefix) {
        result += isLast ? '└── ' : '├── ';
      }
      
      // Add the node name
      result += node.name;
      
      // Add capabilities if any - 不添加括号，优化描述显示
      if (node.capabilities) {
        // 确保文件/目录名和描述之间有合适的空格
        result += ' - '; // 使用更明确的分隔符
        // 根据名称长度调整空格，使描述对齐
        const nameLength = node.name.length;
        if (nameLength < 10) {
          result += ' '.repeat(10 - nameLength);
        }
        result += node.capabilities;
      }
      
      result += '\\n';
      
      // Process children if it's a folder
      if (node.children && node.children.length > 0) {
        const childrenPrefix = prefix + (isLast ? '    ' : '│   ');
        node.children.forEach((child, index) => {
          if (child) { // 确保子节点存在
            const isLastChild = index === node.children.length - 1;
            result += generateAsciiTree(child, childrenPrefix, isLastChild, path);
          }
        });
      }
      
      return result;
    };

    try {
      const treeString = generateAsciiTree(treeData);
      setAsciiTree(treeString.replace(/\\n/g, '\n'));
    } catch (error) {
      console.error('生成树结构时出错:', error);
      setAsciiTree('Error: 无法生成树结构显示');
    }
  }, [treeData]);

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(asciiTree).then(() => {
      showNotificationWithMessage('复制到剪贴板成功！');
    });
  };

  const showNotificationWithMessage = (message) => {
    setNotificationMessage(message);
    setShowNotification(true);
    setTimeout(() => setShowNotification(false), 3000);
  };

  const collectCapabilities = (node, path = '') => {
    // 检查节点是否存在
    if (!node || !node.name) {
      return [];
    }
    
    const currentPath = path ? `${path}/${node.name}` : node.name;
    let result = [];
    
    if (node.capabilities) {
      result.push(`${currentPath}: ${node.capabilities}`);
    }
    
    if (node.children && node.children.length > 0) {
      node.children.forEach(child => {
        if (child) { // 确保子节点存在
          result = [...result, ...collectCapabilities(child, currentPath)];
        }
      });
    }
    
    return result;
  };

  const generateAIPrompt = async () => {
    try {
      // 检查是否有可用的树结构
      if (!asciiTree || asciiTree === 'Error: 无法生成树结构') {
        showNotificationWithMessage('当前没有可用的项目结构，无法生成提示词');
        return;
      }
      
      // Reset enhancement state
      setEnhancementAvailable(true);
      
      // Create a comprehensive prompt based on tree structure
      const capabilities = collectCapabilities(treeData);
      
      // 检查是否有功能描述
      if (capabilities.length === 0) {
        showNotificationWithMessage('项目结构中缺少功能描述，无法生成完整的提示词');
        return;
      }
      
      // 递归获取所有文件名
      const collectFileNames = (node, path = '') => {
        if (!node) return [];
        
        const currentPath = path ? `${path}/${node.name}` : node.name;
        let files = [];
        
        if (node.type === 'file') {
          files.push(currentPath);
        }
        
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => {
            if (child) { // 确保子节点存在
              files = [...files, ...collectFileNames(child, currentPath)];
            }
          });
        }
        
        return files;
      };
      
      const detectedFiles = treeData ? collectFileNames(treeData) : [];
      
      // 使用promptManager生成项目开发提示词
      const result = await promptManager.generateProjectDevelopmentPrompt(
        treeData, 
        asciiTree, 
        capabilities, 
        detectedFiles
      );
      
      setPromptText(result.prompt);
      setShowModal(true);
    } catch (error) {
      console.error('生成AI提示词时出错:', error);
      showNotificationWithMessage('生成AI提示词失败，请重试');
    }
  };

  const handleModalClose = () => {
    setShowModal(false);
    setShowComparison(false);
  };

  const handlePromptCopy = () => {
    try {
      // 使用当前选中的提示词
      const textToCopy = activePanel === 'original' ? originalPrompt : enhancedPrompt;
      navigator.clipboard.writeText(textToCopy).then(() => {
        showNotificationWithMessage('AI提示词已复制到剪贴板！');
        setShowModal(false);
        setShowComparison(false);
      });
    } catch (error) {
      console.error('复制提示词失败:', error);
      showNotificationWithMessage('复制提示词失败，请手动复制');
    }
  };

  const handleSendToNoCode = () => {
    // NoCode的URL
    const baseUrl = 'https://nocode.sankuai.com/#/';
    
    try {
      // 使用当前选中的提示词
      const textToCopy = activePanel === 'original' ? originalPrompt : enhancedPrompt;
      // 首先将提示词复制到剪贴板
      navigator.clipboard.writeText(textToCopy).then(() => {
        // 显示通知
        showNotificationWithMessage('提示词已复制，即将打开NoCode网站');
        
        // 设置一个短暂的延迟，让用户看到通知
        setTimeout(() => {
          // 打开NoCode网站
          window.open(baseUrl, '_blank');
        }, 800);
      }).catch(error => {
        console.error('复制失败:', error);
        showNotificationWithMessage('复制提示词失败，请手动复制后打开NoCode');
      });
    } catch (error) {
      console.error('发送到NoCode失败:', error);
      showNotificationWithMessage('发送失败，请手动复制后打开NoCode');
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 开始对比视图
  const startComparison = () => {
    setOriginalPrompt(promptText);
    setEnhancedPrompt('');
    setStreamProgress(0);
    setActivePanel('enhanced');
    setShowComparison(true);
    enhancePromptWithStream();
  };

  // 使用流式输出增强提示词
  const enhancePromptWithStream = async () => {
    if (isEnhancing) return;
    
    try {
      setIsEnhancing(true);
      setStreamProgress(0);
      
      // 使用promptManager进行增强
      const enhancedText = await promptManager.enhancePrompt(
        promptText,
        (chunk, fullText) => {
          // 更新文本
          setEnhancedPrompt(fullText);
          
          // 检查是否完整
          const isComplete = checkCompleteness(fullText);
          
          // 估算进度，确保进度显示更准确
          let percentage = 0;
          if (isComplete) {
            percentage = 100;
          } else {
            // 计算基于内容长度的进度
            let baseProgress = Math.min(Math.floor((fullText.length / (promptText.length * 1.2)) * 100), 95);
            
            // 结构检测进度增量
            if (fullText.includes('## 项目结构') || fullText.includes('# 项目结构')) {
              percentage = Math.max(baseProgress, 30);
            }
            if (fullText.includes('## 功能描述') || fullText.includes('# 功能描述')) {
              percentage = Math.max(baseProgress, 50);
            }
            if (fullText.includes('## 模块交互') || fullText.includes('# 模块交互')) {
              percentage = Math.max(baseProgress, 70);
            }
            if (fullText.includes('## 技术栈') || fullText.includes('# 技术栈')) {
              percentage = Math.max(baseProgress, 85);
            }
          }
          
          setStreamProgress(percentage);
        }
      );
      
      // 流式响应完成
      setStreamProgress(100);
      setEnhancedPrompt(enhancedText);
      setEnhancementAvailable(false);
      setTimeout(() => {
        setActivePanel('enhanced');
      }, 500);

      // 异步生成摘要（不阻塞UI）
      generateSummariesAsync(promptText, enhancedText);
      
    } catch (error) {
      console.error('增强提示词时出错:', error);
      showNotificationWithMessage('提示词增强失败，请重试');
    } finally {
      setIsEnhancing(false);
    }
  };

  // 检测输出是否完整的函数
  const checkCompleteness = (text) => {
    // 内容长度检查 - 如果超过原文长度且内容看起来完整
    const lengthCheck = text.length > promptText.length * 0.9;
    
    // 结构完整性检查 - 检查关键节段是否存在且结构完整
    const hasProjectStructure = text.includes('## 项目结构') || text.includes('# 项目结构');
    const hasCapabilities = text.includes('## 功能描述') || text.includes('# 功能描述');
    const hasDataFlow = text.includes('## 模块交互') || text.includes('# 模块交互');
    const structureComplete = hasProjectStructure && hasCapabilities && hasDataFlow;
    
    // 代码块完整性检查 - 确保所有代码块都正确闭合
    const codeBlocksComplete = text.split('```').length % 2 === 1;
    
    // 自然结束标志检查 - 寻找最后一段是否包含预期输出等结束性段落
    const hasEnding = text.includes('## 预期输出') || text.includes('# 预期输出');
    
    // 组合判断
    return (lengthCheck && codeBlocksComplete) || (structureComplete && hasEnding);
  };
  
  // 使用大模型增强提示词（非对比视图版本）
  const enhancePromptWithLLM = async () => {
    if (isEnhancing) return;
    
    try {
      setIsEnhancing(true);
      console.log('开始AI增强处理...');
      
      // 使用promptManager的JSON增强方法
      const result = await promptManager.enhancePromptJson(
        promptText,
        (text, progress) => {
          console.log('收到流式更新:', {
            textLength: text.length,
            hasJson: progress.json !== null,
            progress: progress.progress
          });
          
          // 如果已经解析出了JSON，更新UI
          if (progress.json && progress.json.optimizedPrompt) {
            console.log('成功解析到JSON结构');
            setPromptText(progress.json.optimizedPrompt.trim());
          }
          // 更新进度
          setStreamProgress(progress.progress || 0);
        }
      );
      
      if (result && result.optimizedPrompt) {
        console.log('处理成功，更新UI');
        const originalPrompt = promptText;
        const enhancedPrompt = result.optimizedPrompt.trim();
        setPromptText(enhancedPrompt);
        showNotificationWithMessage('提示词已使用AI增强！');
        setEnhancementAvailable(false);

        // 异步生成摘要（不阻塞UI）
        generateSummariesAsync(originalPrompt, enhancedPrompt);
      } else {
        console.error('没有获取到有效的结果');
        throw new Error('增强结果为空或格式不正确');
      }
    } catch (error) {
      console.error('增强提示词时出错:', error);
      showNotificationWithMessage('提示词增强失败，请重试');
    } finally {
      setIsEnhancing(false);
      setStreamProgress(0);
    }
  };

  // 测试摘要弹窗功能（使用模拟数据）
  const testSummaryModal = () => {
    console.log('🧪 测试摘要弹窗功能...');
    const mockSummaryData = {
      originalSummary: "这是一个测试的原始提示词摘要。该提示词主要用于生成项目结构，包含了详细的文件描述和组织方式。",
      enhancedSummary: "这是一个测试的增强提示词摘要。增强后的提示词更加具体和详细，包含了更多的上下文信息和明确的指导原则。",
      saved: true,
      metadata: {
        originalLength: 500,
        enhancedLength: 800,
        tokenCount: 200,
        processingTime: Date.now()
      }
    };

    setSummaryData(mockSummaryData);
    setShowSummaryModal(true);
    console.log('🧪 测试弹窗已显示');
  };

  // 异步生成摘要（不阻塞UI）
  const generateSummariesAsync = async (originalPrompt, enhancedPrompt) => {
    try {
      console.log('🔄 开始异步生成摘要...', {
        originalPromptLength: originalPrompt.length,
        enhancedPromptLength: enhancedPrompt.length
      });

      // 先显示测试弹窗，验证UI功能
      console.log('🧪 首先测试弹窗功能...');
      setTimeout(() => {
        testSummaryModal();
      }, 2000); // 2秒后显示测试弹窗

      // 异步调用摘要服务，添加回调处理
      promptSummaryService.processPromptSummaries(originalPrompt, enhancedPrompt, {
        metadata: {
          source: 'tree_output_enhancement',
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        },
        onSummaryGenerated: (result) => {
          console.log('✅ 摘要生成完成，准备展示弹窗:', {
            originalSummaryLength: result.originalSummary?.length || 0,
            enhancedSummaryLength: result.enhancedSummary?.length || 0,
            saved: result.saved,
            result: result
          });

          // 设置摘要数据并显示弹窗
          console.log('🎯 设置摘要数据并显示弹窗...');
          setSummaryData(result);
          setShowSummaryModal(true);
          console.log('🎯 弹窗状态已设置为true');
        }
      }).then(result => {
        console.log('📋 摘要服务Promise完成:', result);
      }).catch(error => {
        console.error('⚠️ 摘要生成失败，但不影响主流程:', error);
        // 如果真实摘要生成失败，也显示测试弹窗
        console.log('🧪 摘要生成失败，显示测试弹窗作为备用...');
        setTimeout(() => {
          testSummaryModal();
        }, 1000);
      });

    } catch (error) {
      console.error('⚠️ 启动摘要生成失败:', error);
      // 如果启动失败，显示测试弹窗
      console.log('🧪 启动失败，显示测试弹窗作为备用...');
      setTimeout(() => {
        testSummaryModal();
      }, 1000);
    }
  };

  // 接受增强版本
  const acceptEnhanced = () => {
    setPromptText(enhancedPrompt);
    setShowComparison(false);
  };
  
  // 拒绝增强版本
  const rejectEnhanced = () => {
    // 保持原有版本
    setPromptText(originalPrompt);
    setShowComparison(false);
    setEnhancementAvailable(true); // 允许重新尝试增强
  };
  
  // 切换活动面板
  const switchActivePanel = () => {
    setActivePanel(activePanel === 'original' ? 'enhanced' : 'original');
  };

  // 复制指定面板的内容
  const copyPanelContent = (panel) => {
    const textToCopy = panel === 'original' ? originalPrompt : enhancedPrompt;
    navigator.clipboard.writeText(textToCopy).then(() => {
      showNotificationWithMessage(`已复制${panel === 'original' ? '原始' : 'AI增强'}Prompt到剪贴板`);
    }).catch(error => {
      console.error('复制失败:', error);
      showNotificationWithMessage('复制失败，请手动复制');
    });
  };

  // 添加拖动处理函数
  const handleDragStart = (e) => {
    e.preventDefault();
    setIsDragging(true);
    
    const handleDrag = (moveEvent) => {
      if (comparisonBodyRef.current) {
        const containerWidth = comparisonBodyRef.current.clientWidth;
        const newPosition = (moveEvent.clientX - comparisonBodyRef.current.getBoundingClientRect().left) / containerWidth * 100;
        // 限制拖动范围在20%-80%之间，确保两侧都有最小宽度
        const boundedPosition = Math.max(20, Math.min(80, newPosition));
        setDividerPosition(boundedPosition);
      }
    };
    
    const handleDragEnd = () => {
      setIsDragging(false);
      document.removeEventListener('mousemove', handleDrag);
      document.removeEventListener('mouseup', handleDragEnd);
    };
    
    document.addEventListener('mousemove', handleDrag);
    document.addEventListener('mouseup', handleDragEnd);
  };
  
  // 同步滚动函数
  const syncScroll = (sourceRef, targetRef) => {
    if (sourceRef.current && targetRef.current) {
      const sourceElement = sourceRef.current;
      const targetElement = targetRef.current;
      
      // 计算滚动位置百分比
      const scrollPercentage = sourceElement.scrollTop / 
                               (sourceElement.scrollHeight - sourceElement.clientHeight);
      
      // 应用到目标元素
      targetElement.scrollTop = scrollPercentage * 
                               (targetElement.scrollHeight - targetElement.clientHeight);
    }
  };
  
  // 左侧面板滚动处理
  const handleLeftScroll = () => {
    syncScroll(leftPanelRef, rightPanelRef);
  };
  
  // 右侧面板滚动处理
  const handleRightScroll = () => {
    syncScroll(rightPanelRef, leftPanelRef);
  };

  return (
    <OutputContainer>
      <OutputHeader>
        <OutputTitle>项目结构预览</OutputTitle>
        <ButtonsGroup>
          <ActionButton onClick={generateAIPrompt}>
            <FaRobot />
            生成规则Prompt
          </ActionButton>
          <ActionButton primary onClick={handleCopyToClipboard}>
            <FaCopy />
            复制到剪贴板
          </ActionButton>
        </ButtonsGroup>
      </OutputHeader>
      <TreeDisplay>{asciiTree}</TreeDisplay>
      
      <NotificationMessage show={showNotification}>
        {notificationMessage}
      </NotificationMessage>

      {showModal && (
        <Modal>
          <ModalContent style={{
            width: isFullscreen ? '95%' : '80%',
            height: isFullscreen ? '95vh' : '70vh',
            maxWidth: isFullscreen ? '100%' : '900px',
          }}>
            <ModalHeader>
              <ModalTitle>AI增强Prompt</ModalTitle>
              <ModalActions>
                {enhancementAvailable && !showComparison && (
                  <IconButton 
                    onClick={startComparison}
                    title="使用AI增强Prompt"
                    disabled={isEnhancing}
                    style={{ color: isEnhancing ? '#a0aec0' : '#3182ce' }}
                  >
                    <FaMagic />
                  </IconButton>
                )}
                <IconButton 
                  onClick={toggleFullscreen}
                  title={isFullscreen ? "退出全屏" : "全屏模式"}
                >
                  {isFullscreen ? <FaCompressAlt /> : <FaExpandAlt />}
                </IconButton>
                <IconButton 
                  onClick={handlePromptCopy}
                  title="复制到剪贴板"
                >
                  <FaCopy />
                </IconButton>
                <IconButton 
                  onClick={handleModalClose}
                  title="关闭"
                >
                  <FaTimes />
                </IconButton>
              </ModalActions>
            </ModalHeader>
            
            {!showComparison ? (
              // 普通视图
              <ModalBody>
                <p style={{ marginBottom: '12px', color: '#4a5568', display: 'flex', alignItems: 'center', gap: '6px' }}>
                  下面是根据你的项目结构生成的Rule Prompt，你可以自由编辑后再复制或发送给AI Coding助手以快速构建项目：
                  <InfoTooltip data-tooltip="提示词中包含了项目的详细结构和各组件的功能说明，可以直接发送给AI助手以生成完整的项目代码。">?</InfoTooltip>
                </p>
                <p style={{ marginBottom: '12px', color: '#4a5568', fontSize: '13px' }}>
                  <FaMagic style={{ color: '#3182ce', marginRight: '5px' }} />
                  点击增强按钮可使用大模型优化Prompt，提升结构清晰度和生成代码质量
                </p>
                <TextArea 
                  value={promptText} 
                  onChange={(e) => setPromptText(e.target.value)}
                />
                
                <ButtonGroup>
                  <Button onClick={handleModalClose}>关闭</Button>
                  {enhancementAvailable && (
                    <Button onClick={startComparison} disabled={isEnhancing}>
                      {isEnhancing ? <LoadingIndicator size="14px" style={{ marginRight: '5px' }} /> : <FaMagic style={{ marginRight: '5px' }} />}
                      {isEnhancing ? '增强中...' : 'AI增强Prompt'}
                    </Button>
                  )}
                  <Button onClick={handlePromptCopy}>
                    <FaCopy />
                    复制提示词
                  </Button>
                  <Button primary onClick={handleSendToNoCode}>
                    <FaExternalLinkAlt />
                    发送到NoCode平台
                  </Button>
                </ButtonGroup>
              </ModalBody>
            ) : (
              // 对比视图
              <ComparisonContainer>
                <ComparisonHeader>
                  <ComparisonTitle>
                    AI增强Prompt - 查看原始与优化后的版本
                    <InfoTooltip data-tooltip="AI增强优化了以下方面：\n- 项目结构描述的清晰度和专业性\n- 功能描述的详细程度和关联性\n- 技术要求的准确性和完整性\n- 整体提示词的层次结构和可读性">i</InfoTooltip>
                  </ComparisonTitle>
                </ComparisonHeader>
                
                <ComparisonBody ref={comparisonBodyRef}>
                  <PromptPanel 
                    showBorder={false} 
                    isOriginal={true}
                    style={{ flex: `0 0 ${dividerPosition}%` }}
                  >
                    <PanelHeader active={activePanel === 'original'}>
                      <PanelTitleGroup>
                        <PanelTitle active={activePanel === 'original'}>
                          原始Prompt
                        </PanelTitle>
                      </PanelTitleGroup>
                      <PanelActions>
                        <PanelButton 
                          onClick={() => copyPanelContent('original')} 
                          title="复制原始提示词"
                        >
                          <FaCopy />
                        </PanelButton>
                        <PanelButton 
                          onClick={() => setActivePanel('original')}
                          title="编辑此版本"
                          disabled={activePanel === 'original'}
                          style={{ color: activePanel === 'original' ? '#3182ce' : '#718096' }}
                        >
                          <FaEdit />
                        </PanelButton>
                      </PanelActions>
                    </PanelHeader>
                    <PanelContent 
                      ref={leftPanelRef}
                      onScroll={handleLeftScroll}
                    >
                      <Editor 
                        value={originalPrompt} 
                        onChange={(e) => setOriginalPrompt(e.target.value)}
                        readOnly={activePanel !== 'original'}
                      />
                    </PanelContent>
                  </PromptPanel>
                  
                  <DraggableDivider 
                    onMouseDown={handleDragStart}
                  />
                  
                  <PromptPanel 
                    isOriginal={false}
                    style={{ flex: `0 0 ${100 - dividerPosition}%` }}
                  >
                    <PanelHeader active={activePanel === 'enhanced'}>
                      <PanelTitleGroup>
                        <PanelTitle active={activePanel === 'enhanced'}>
                          AI增强Prompt
                          {isEnhancing && (
                            <StatusBadge loading>
                              <SmallLoadingIndicator size="10px" />
                              增强中 {Math.floor(streamProgress)}%
                            </StatusBadge>
                          )}
                          {!isEnhancing && enhancedPrompt && (
                            <StatusBadge success>
                              完成
                            </StatusBadge>
                          )}
                        </PanelTitle>
                      </PanelTitleGroup>
                      <PanelActions>
                        <PanelButton 
                          onClick={() => copyPanelContent('enhanced')} 
                          title="复制增强提示词"
                          disabled={isEnhancing || !enhancedPrompt}
                        >
                          <FaCopy />
                        </PanelButton>
                        <PanelButton 
                          onClick={() => setActivePanel('enhanced')}
                          title="编辑此版本"
                          disabled={activePanel === 'enhanced' || isEnhancing || !enhancedPrompt}
                          style={{ color: activePanel === 'enhanced' ? '#3182ce' : '#718096' }}
                        >
                          <FaEdit />
                        </PanelButton>
                      </PanelActions>
                    </PanelHeader>
                    <PanelContent 
                      ref={rightPanelRef}
                      onScroll={handleRightScroll}
                    >
                      <Editor 
                        value={enhancedPrompt} 
                        onChange={(e) => setEnhancedPrompt(e.target.value)}
                        readOnly={isEnhancing || activePanel !== 'enhanced'}
                      />
                      {isEnhancing && (
                        <StreamProgressIndicator progress={streamProgress} />
                      )}
                      
                      {!isEnhancing && enhancedPrompt && (
                        <EnhancementInfo>
                          <strong>AI已对提示词进行了以下优化：</strong>
                          <EnhancementPoint>更清晰的项目结构描述，明确展示了组件层级关系</EnhancementPoint>
                          <EnhancementPoint>增强了功能描述的专业性和技术准确度</EnhancementPoint>
                          <EnhancementPoint>改进了整体提示词的结构和格式，使其更易于理解</EnhancementPoint>
                        </EnhancementInfo>
                      )}
                    </PanelContent>
                  </PromptPanel>
                </ComparisonBody>
                
                <ComparisonFooter>
                  <ComparisonButton onClick={switchActivePanel} disabled={isEnhancing}>
                    <FaExchangeAlt />
                    切换编辑面板
                  </ComparisonButton>
                  
                  <FooterActions>
                    <ComparisonButton onClick={rejectEnhanced} disabled={isEnhancing}>
                      <FaReject />
                      保留原始版本
                    </ComparisonButton>
                    <ComparisonButton primary onClick={handleSendToNoCode} disabled={isEnhancing}>
                      <FaExternalLinkAlt />
                      发送到NoCode平台
                    </ComparisonButton>
                    <ComparisonButton success onClick={acceptEnhanced} disabled={isEnhancing || !enhancedPrompt}>
                      <FaCheck />
                      采用AI增强Prompt
                    </ComparisonButton>
                  </FooterActions>
                </ComparisonFooter>
              </ComparisonContainer>
            )}
            
            <div style={{ 
              fontSize: '12px', 
              color: '#718096', 
              marginTop: '10px', 
              textAlign: 'right' 
            }}>
              提示：可拖拽右下角调整大小
            </div>
          </ModalContent>
        </Modal>
      )}

      {/* 摘要展示Modal */}
      {showSummaryModal && summaryData && (
        <SummaryModal onClick={() => setShowSummaryModal(false)}>
          <SummaryModalContent onClick={(e) => e.stopPropagation()}>
            <SummaryModalHeader>
              <SummaryModalTitle>
                <FaMagic style={{ color: '#3182ce' }} />
                提示词摘要生成完成
              </SummaryModalTitle>
              <IconButton onClick={() => setShowSummaryModal(false)}>
                <FaTimes />
              </IconButton>
            </SummaryModalHeader>

            <SummaryModalBody>
              <SummaryLabel>📝 原始提示词摘要</SummaryLabel>
              <SummaryContent>
                {summaryData.originalSummary}
              </SummaryContent>

              <SummaryLabel>✨ 增强提示词摘要</SummaryLabel>
              <SummaryContent>
                {summaryData.enhancedSummary}
              </SummaryContent>

              <div style={{
                fontSize: '12px',
                color: '#718096',
                marginTop: '12px',
                padding: '8px',
                backgroundColor: '#f7fafc',
                borderRadius: '4px',
                border: '1px solid #e2e8f0'
              }}>
                💡 摘要已自动保存到数据库，您可以在提示词分析页面查看历史记录
              </div>
            </SummaryModalBody>

            <SummaryModalFooter>
              <SummaryCloseButton onClick={() => setShowSummaryModal(false)}>
                <FaCheck />
                知道了
              </SummaryCloseButton>
            </SummaryModalFooter>
          </SummaryModalContent>
        </SummaryModal>
      )}
    </OutputContainer>
  );
};

export default TreeOutput; 