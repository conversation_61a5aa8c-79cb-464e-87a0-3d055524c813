/**
 * 提示词摘要服务 - 处理原始和增强提示词的摘要生成与存储
 */

import { getLLMResponse } from './llmService';
import { elasticsearchService } from './elasticsearchService';
import {
  createOriginalPromptSummaryPrompt,
  createEnhancedPromptSummaryPrompt
} from '../prompts/treeOutput';
import { estimateTokenCount, generateSummaryId } from '../utils/index.js';

/**
 * 提示词摘要服务类
 */
class PromptSummaryService {
  constructor() {
    this.useElasticsearch = true;
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return generateSummaryId();
  }

  /**
   * 生成原始提示词摘要
   * @param {string} originalPrompt - 原始提示词内容
   * @param {Object} options - 配置选项
   * @returns {Promise<string>} - 摘要内容
   */
  async generateOriginalPromptSummary(originalPrompt, options = {}) {
    try {
      console.log('🔍 开始生成原始提示词摘要...');
      
      const summaryPrompt = createOriginalPromptSummaryPrompt(originalPrompt);
      
      const summary = await getLLMResponse(summaryPrompt, {
        temperature: 0.3,
        max_tokens: 1000,
        ...options
      });

      console.log('✅ 原始提示词摘要生成成功');
      return summary.trim();
    } catch (error) {
      console.error('❌ 生成原始提示词摘要失败:', error);
      throw new Error(`原始提示词摘要生成失败: ${error.message}`);
    }
  }

  /**
   * 生成增强提示词摘要
   * @param {string} enhancedPrompt - 增强后的提示词内容
   * @param {Object} options - 配置选项
   * @returns {Promise<string>} - 摘要内容
   */
  async generateEnhancedPromptSummary(enhancedPrompt, options = {}) {
    try {
      console.log('🔍 开始生成增强提示词摘要...');
      
      const summaryPrompt = createEnhancedPromptSummaryPrompt(enhancedPrompt);
      
      const summary = await getLLMResponse(summaryPrompt, {
        temperature: 0.3,
        max_tokens: 1200,
        ...options
      });

      console.log('✅ 增强提示词摘要生成成功');
      return summary.trim();
    } catch (error) {
      console.error('❌ 生成增强提示词摘要失败:', error);
      throw new Error(`增强提示词摘要生成失败: ${error.message}`);
    }
  }

  /**
   * 生成双重摘要（原始和增强）
   * @param {string} originalPrompt - 原始提示词内容
   * @param {string} enhancedPrompt - 增强后的提示词内容
   * @param {Object} options - 配置选项
   * @returns {Promise<Object>} - 包含两个摘要的对象
   */
  async generateDualSummaries(originalPrompt, enhancedPrompt, options = {}) {
    try {
      console.log('🔄 开始生成双重摘要...');
      
      // 并行生成两个摘要以提高效率
      const [originalSummary, enhancedSummary] = await Promise.all([
        this.generateOriginalPromptSummary(originalPrompt, options),
        this.generateEnhancedPromptSummary(enhancedPrompt, options)
      ]);

      const result = {
        originalSummary,
        enhancedSummary,
        timestamp: new Date().toISOString()
      };

      console.log('✅ 双重摘要生成完成');
      return result;
    } catch (error) {
      console.error('❌ 生成双重摘要失败:', error);
      throw new Error(`双重摘要生成失败: ${error.message}`);
    }
  }

  /**
   * 保存摘要数据到Elasticsearch
   * @param {Object} summaryData - 摘要数据
   * @param {string} summaryData.originalPrompt - 原始提示词
   * @param {string} summaryData.enhancedPrompt - 增强提示词
   * @param {string} summaryData.originalSummary - 原始提示词摘要
   * @param {string} summaryData.enhancedSummary - 增强提示词摘要
   * @param {Object} metadata - 额外的元数据
   * @returns {Promise<Object>} - 保存结果
   */
  async saveSummaryToElasticsearch(summaryData, metadata = {}) {
    if (!this.useElasticsearch) {
      console.log('⚠️ Elasticsearch未启用，跳过摘要保存');
      return null;
    }

    try {
      console.log('💾 保存摘要数据到Elasticsearch...');

      // 合并原始和增强摘要为单一摘要内容
      const combinedSummary = `原始提示词摘要：\n${summaryData.originalSummary}\n\n增强提示词摘要：\n${summaryData.enhancedSummary}`;

      // 计算token数量（简单估算：中文按字符数，英文按单词数）
      const tokenCount = estimateTokenCount(summaryData.originalPrompt + summaryData.enhancedPrompt);

      const document = {
        id: this.generateId(),
        content: summaryData.enhancedPrompt, // 存储增强后的提示词作为主要内容
        type: 'enhanced',
        summary: combinedSummary,
        timestamp: new Date().toISOString(),
        metadata: {
          tokenCount: tokenCount,
          language: metadata.language || 'zh-CN'
        }
      };

      // 使用prompts索引存储摘要数据
      const response = await elasticsearchService.savePrompt(document);

      console.log('✅ 摘要数据保存成功');
      return response;
    } catch (error) {
      console.error('❌ 保存摘要数据失败:', error);
      // 不抛出错误，避免影响主流程
      return null;
    }
  }



  /**
   * 完整的摘要生成和存储流程
   * @param {string} originalPrompt - 原始提示词内容
   * @param {string} enhancedPrompt - 增强后的提示词内容
   * @param {Object} options - 配置选项
   * @param {Function} options.onSummaryGenerated - 摘要生成完成后的回调函数
   * @returns {Promise<Object>} - 完整的处理结果
   */
  async processPromptSummaries(originalPrompt, enhancedPrompt, options = {}) {
    try {
      console.log('🚀 开始完整的摘要处理流程...');
      
      // 生成双重摘要
      const summaries = await this.generateDualSummaries(originalPrompt, enhancedPrompt, options);
      
      // 准备存储数据
      const summaryData = {
        originalPrompt,
        enhancedPrompt,
        originalSummary: summaries.originalSummary,
        enhancedSummary: summaries.enhancedSummary
      };

      // 异步保存到Elasticsearch（不阻塞主流程）
      this.saveSummaryToElasticsearch(summaryData, options.metadata)
        .catch(error => {
          console.error('⚠️ 异步保存摘要失败，但不影响主流程:', error);
        });

      const result = {
        ...summaries,
        saved: true,
        metadata: {
          originalLength: originalPrompt.length,
          enhancedLength: enhancedPrompt.length,
          tokenCount: estimateTokenCount(originalPrompt + enhancedPrompt),
          processingTime: Date.now()
        }
      };

      // 如果提供了回调函数，调用它
      if (options.onSummaryGenerated && typeof options.onSummaryGenerated === 'function') {
        try {
          console.log('📞 准备调用摘要生成回调函数...');
          options.onSummaryGenerated(result);
          console.log('📞 摘要生成回调函数调用成功');
        } catch (callbackError) {
          console.error('⚠️ 摘要生成回调执行失败:', callbackError);
        }
      } else {
        console.warn('⚠️ 没有提供摘要生成回调函数或回调函数类型不正确:', {
          hasCallback: !!options.onSummaryGenerated,
          callbackType: typeof options.onSummaryGenerated
        });
      }

      console.log('✅ 摘要处理流程完成');
      return result;
    } catch (error) {
      console.error('❌ 摘要处理流程失败:', error);
      throw error;
    }
  }

  /**
   * 设置是否使用Elasticsearch
   * @param {boolean} enabled - 是否启用
   */
  setElasticsearchEnabled(enabled) {
    this.useElasticsearch = enabled;
    console.log(`📊 Elasticsearch存储已${enabled ? '启用' : '禁用'}`);
  }
}

// 导出单例实例
export const promptSummaryService = new PromptSummaryService();

export default PromptSummaryService;
