/**
 * ElasticSearch服务 - 处理提示词相关存储与检索
 */

// @ts-nocheck

// import { Client } from '@elastic/elasticsearch'; // Temporarily disabled for browser compatibility
import { elasticsearchConfig } from '../config/elasticsearch';
import { estimateTokenCount } from '../utils/tokenUtils.js';

/**
 * ElasticSearch服务类
 */
class ElasticsearchService {
  constructor() {
    // 配置ElasticSearch客户端
    const clientConfig = {
      node: elasticsearchConfig.node,
      requestTimeout: elasticsearchConfig.connection.requestTimeout,
      pingTimeout: elasticsearchConfig.connection.pingTimeout,
      maxRetries: elasticsearchConfig.connection.maxRetries
    };
    if (elasticsearchConfig.auth.username && elasticsearchConfig.auth.password) {
      clientConfig.auth = elasticsearchConfig.auth;
    }
    // this.client = new Client(clientConfig); // Temporarily disabled for browser compatibility
    console.log('ElasticsearchService: Using HTTP fallback mode instead of Elasticsearch client');

    // 索引名称
    this.promptsIndex = elasticsearchConfig.indices.prompts;
    this.evaluationsIndex = elasticsearchConfig.indices.evaluations;

    // 初始化状态管理
    this.initializationPromise = null;
    this.isInitialized = false;
    this.initializationError = null;

    // 启动异步初始化（不阻塞构造函数）
    this.startInitialization();
  }

  /**
   * 启动异步初始化
   */
  startInitialization() {
    this.initializationPromise = this.initializeIndices()
      .then(() => {
        this.isInitialized = true;
        console.log('✅ ElasticSearch索引初始化完成');
        return true;
      })
      .catch((error) => {
        this.initializationError = error;
        console.error('❌ ElasticSearch索引初始化失败:', error);
        throw error;
      });
  }

  /**
   * 初始化ElasticSearch索引
   */
  async initializeIndices() {
    try {
      // 检查并创建prompts索引
      await this.createIndexIfNotExists(this.promptsIndex, this.getPromptsMapping());

      // 检查并创建evaluations索引
      await this.createIndexIfNotExists(this.evaluationsIndex, this.getEvaluationsMapping());

    } catch (error) {
      console.error('索引初始化过程中出错:', error);
      throw error;
    }
  }

  /**
   * 确保索引已初始化（延迟初始化）
   */
  async ensureInitialized() {
    if (this.isInitialized) {
      return true;
    }

    if (this.initializationError) {
      throw new Error(`ElasticSearch初始化失败: ${this.initializationError.message}`);
    }

    if (this.initializationPromise) {
      await this.initializationPromise;
      return true;
    }

    // 如果没有初始化Promise，重新启动初始化
    this.startInitialization();
    await this.initializationPromise;
    return true;
  }

  /**
   * 创建索引（如果不存在）
   */
  async createIndexIfNotExists(indexName, mapping) {
    try {
      // 检查是否有可用的 Elasticsearch 客户端
      if (!this.client) {
        console.log(`⚠️ Elasticsearch 客户端不可用，跳过索引 ${indexName} 的创建`);
        return;
      }

      const exists = await this.client.indices.exists({ index: indexName });

      if (!exists) {
        await this.client.indices.create({
          index: indexName,
          body: mapping
        });
        console.log(`索引 ${indexName} 创建成功`);
      }
    } catch (error) {
      console.error(`创建索引 ${indexName} 失败:`, error);
      // 不抛出错误，允许应用继续运行
      console.log(`⚠️ 将使用 HTTP 代理模式访问 Elasticsearch`);
    }
  }

  /**
   * 获取prompts索引的映射配置
   */
  getPromptsMapping() {
    return {
      mappings: {
        properties: {
          id: { type: 'keyword' },
          content: {
            type: 'text',
            analyzer: 'standard',
            fields: {
              keyword: { type: 'keyword' }
            }
          },
          type: { type: 'keyword' }, // 'original' 或 'enhanced'
          score: { type: 'float' },
          timestamp: { type: 'date' },
          summary: {
            type: 'text',
            analyzer: 'standard',
            fields: {
              keyword: { type: 'keyword' }
            }
          },
          metadata: {
            properties: {
              tokenCount: { type: 'integer' },
              language: { type: 'keyword' }
            }
          }
        }
      }
    };
  }

  /**
   * 获取evaluations索引的映射配置
   */
  getEvaluationsMapping() {
    return {
      mappings: {
        properties: {
          id: { type: 'keyword' },
          workflowId: { type: 'keyword' },
          timestamp: { type: 'date' },
          originalPrompt: {
            properties: {
              content: { type: 'text' },
              score: { type: 'float' },
              evaluation: {
                properties: {
                  finalScore: { type: 'float' },
                  llmEvaluation: {
                    properties: {
                      specificity: { type: 'float' },
                      clarity: { type: 'float' },
                      structure: { type: 'float' },
                      completeness: { type: 'float' },
                      roleDefinition: { type: 'float' },
                      outputFormat: { type: 'float' },
                      constraints: { type: 'float' },
                      actionability: { type: 'float' }
                    }
                  },
                  humanEvaluation: {
                    properties: {
                      specificity: { type: 'float' },
                      clarity: { type: 'float' },
                      structure: { type: 'float' },
                      completeness: { type: 'float' },
                      roleDefinition: { type: 'float' },
                      outputFormat: { type: 'float' },
                      constraints: { type: 'float' },
                      actionability: { type: 'float' }
                    }
                  }
                }
              }
            }
          },
          enhancedPrompt: {
            properties: {
              content: { type: 'text' },
              score: { type: 'float' },
              evaluation: {
                properties: {
                  finalScore: { type: 'float' },
                  llmEvaluation: {
                    properties: {
                      specificity: { type: 'float' },
                      clarity: { type: 'float' },
                      structure: { type: 'float' },
                      completeness: { type: 'float' },
                      roleDefinition: { type: 'float' },
                      outputFormat: { type: 'float' },
                      constraints: { type: 'float' },
                      actionability: { type: 'float' }
                    }
                  },
                  humanEvaluation: {
                    properties: {
                      specificity: { type: 'float' },
                      clarity: { type: 'float' },
                      structure: { type: 'float' },
                      completeness: { type: 'float' },
                      roleDefinition: { type: 'float' },
                      outputFormat: { type: 'float' },
                      constraints: { type: 'float' },
                      actionability: { type: 'float' }
                    }
                  }
                }
              }
            }
          },
          improvement: {
            properties: {
              absoluteImprovement: { type: 'float' },
              relativeImprovement: { type: 'float' },
              improvementLevel: { type: 'keyword' },
              summary: { type: 'text' }
            }
          },
          metadata: {
            properties: {
              evaluationMethod: { type: 'keyword' },
              scoringSystem: { type: 'keyword' },
              version: { type: 'keyword' }
            }
          }
        }
      }
    };
  }

  /**
   * 保存提示词到ElasticSearch
   */
  async savePrompt(promptData) {
    try {
      // 确保索引已初始化
      await this.ensureInitialized();

      const document = {
        ...promptData,
        timestamp: new Date().toISOString(),
        metadata: {
          tokenCount: promptData.metadata?.tokenCount || estimateTokenCount(promptData.content || ''),
          language: promptData.metadata?.language || 'zh-CN'
        }
      };

      // 检查是否有可用的 Elasticsearch 客户端
      if (!this.client) {
        console.log(`⚠️ Elasticsearch 客户端不可用，使用 HTTP 代理保存提示词`);
        // 使用 HTTP 代理方式保存
        const response = await fetch('http://localhost:9202/prompts/_doc/' + promptData.id, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(document)
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log(`提示词保存成功（HTTP代理），ID: ${promptData.id}`);
        return result;
      }

      const response = await this.client.index({
        index: this.promptsIndex,
        id: promptData.id,
        body: document
      });

      console.log(`提示词保存成功，ID: ${promptData.id}`);
      return response;
    } catch (error) {
      console.error('保存提示词失败:', error);
      throw error;
    }
  }

  /**
   * 保存评估结果到ElasticSearch
   */
  async saveEvaluation(evaluationData) {
    try {
      // 确保索引已初始化
      await this.ensureInitialized();

      const document = {
        ...evaluationData,
        timestamp: new Date().toISOString()
      };

      // 检查是否有可用的 Elasticsearch 客户端
      if (!this.client) {
        console.log(`⚠️ Elasticsearch 客户端不可用，使用 HTTP 代理保存评估结果`);
        // 使用 HTTP 代理方式保存
        const response = await fetch('http://localhost:9202/evaluations/_doc/' + evaluationData.id, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(document)
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log(`评估结果保存成功（HTTP代理），ID: ${evaluationData.id}`);
        return result;
      }

      const response = await this.client.index({
        index: this.evaluationsIndex,
        id: evaluationData.id,
        body: document
      });

      console.log(`评估结果保存成功，ID: ${evaluationData.id}`);
      return response;
    } catch (error) {
      console.error('保存评估结果失败:', error);
      throw error;
    }
  }

  /**
   * 搜索提示词
   */
  async searchPrompts(query, options = {}) {
    try {
      // 确保索引已初始化
      await this.ensureInitialized();

      const searchBody = {
        query: {
          bool: {
            should: [
              {
                match: {
                  content: {
                    query: query,
                    boost: 2.0
                  }
                }
              },
              {
                match: {
                  'content.keyword': {
                    query: query,
                    boost: 1.5
                  }
                }
              }
            ]
          }
        },
        sort: [
          { score: { order: 'desc' } },
          { timestamp: { order: 'desc' } }
        ],
        size: options.size || 10,
        from: options.from || 0
      };

      // 添加过滤条件
      if (options.type) {
        searchBody.query.bool.filter = [
          { term: { type: options.type } }
        ];
      }

      if (options.minScore) {
        searchBody.query.bool.filter = searchBody.query.bool.filter || [];
        searchBody.query.bool.filter.push({
          range: { score: { gte: options.minScore } }
        });
      }

      // 检查是否有可用的 Elasticsearch 客户端
      if (!this.client) {
        console.log(`⚠️ Elasticsearch 客户端不可用，使用 HTTP 代理搜索提示词`);
        // 使用 HTTP 代理方式搜索
        const response = await fetch('http://localhost:9202/prompts/_search', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(searchBody)
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        return {
          total: result.hits.total.value,
          prompts: result.hits.hits.map(hit => ({
            id: hit._id,
            score: hit._score,
            ...hit._source
          }))
        };
      }

      const response = await this.client.search({
        index: this.promptsIndex,
        body: searchBody
      });

      return {
        total: response.body.hits.total.value,
        prompts: response.body.hits.hits.map(hit => ({
          id: hit._id,
          score: hit._score,
          ...hit._source
        }))
      };
    } catch (error) {
      console.error('搜索提示词失败:', error);
      throw error;
    }
  }

  /**
   * 获取评估统计信息
   */
  async getEvaluationStatistics() {
    try {
      // 确保索引已初始化
      await this.ensureInitialized();

      const searchBody = {
        size: 0,
        aggs: {
          total_evaluations: {
            value_count: { field: 'id' }
          },
          avg_original_score: {
            avg: { field: 'originalPrompt.score' }
          },
          avg_enhanced_score: {
            avg: { field: 'enhancedPrompt.score' }
          },
          avg_improvement: {
            avg: { field: 'improvement.absoluteImprovement' }
          },
          improvement_levels: {
            terms: { field: 'improvement.improvementLevel' }
          },
          score_distribution: {
            histogram: {
              field: 'enhancedPrompt.score',
              interval: 10
            }
          }
        }
      };

      let aggs;

      // 检查是否有可用的 Elasticsearch 客户端
      if (!this.client) {
        console.log(`⚠️ Elasticsearch 客户端不可用，使用 HTTP 代理获取评估统计信息`);
        // 使用 HTTP 代理方式获取统计信息
        const response = await fetch('http://localhost:9202/evaluations/_search', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(searchBody)
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        aggs = result.aggregations;
      } else {
        const response = await this.client.search({
          index: this.evaluationsIndex,
          body: searchBody
        });
        aggs = response.body.aggregations;
      }
      
      return {
        totalEvaluations: aggs.total_evaluations.value,
        averageOriginalScore: Math.round(aggs.avg_original_score.value * 100) / 100,
        averageEnhancedScore: Math.round(aggs.avg_enhanced_score.value * 100) / 100,
        averageImprovement: Math.round(aggs.avg_improvement.value * 100) / 100,
        improvementLevels: aggs.improvement_levels.buckets,
        scoreDistribution: aggs.score_distribution.buckets
      };
    } catch (error) {
      console.error('获取评估统计信息失败:', error);
      throw error;
    }
  }


}

// 导出单例实例
export const elasticsearchService = new ElasticsearchService();

export default ElasticsearchService;
