/**
 * Test script for shared utilities
 * Verifies that the refactored utilities work correctly
 */

// Import utilities (using relative path for testing)
import { 
  estimateTokenCount, 
  generateSummaryId, 
  generateWorkflowId, 
  generateDatabaseId,
  generateId
} from '../src/utils/index.js';

console.log('🧪 Testing Shared Utilities...\n');

// Test token counting
console.log('📊 Testing Token Counting:');
const testTexts = [
  'Hello world',
  '你好世界',
  'Hello world 你好世界',
  'This is a longer text with multiple words and some Chinese characters 这是一个更长的文本',
  ''
];

testTexts.forEach(text => {
  const count = estimateTokenCount(text);
  console.log(`  "${text}" -> ${count} tokens`);
});

// Test ID generation
console.log('\n🆔 Testing ID Generation:');
console.log('  Summary ID:', generateSummaryId());
console.log('  Workflow ID:', generateWorkflowId());
console.log('  Database ID:', generateDatabaseId());
console.log('  Generic ID:', generateId('test'));
console.log('  Another Summary ID:', generateSummaryId());

// Test consistency
console.log('\n🔄 Testing Consistency:');
const id1 = generateSummaryId();
const id2 = generateSummaryId();
console.log('  Two summary IDs should be different:');
console.log('    ID1:', id1);
console.log('    ID2:', id2);
console.log('    Different?', id1 !== id2 ? '✅' : '❌');

// Test token counting edge cases
console.log('\n🧪 Testing Edge Cases:');
console.log('  Empty string:', estimateTokenCount(''));
console.log('  Null:', estimateTokenCount(null));
console.log('  Undefined:', estimateTokenCount(undefined));
console.log('  Only spaces:', estimateTokenCount('   '));
console.log('  Only Chinese:', estimateTokenCount('你好世界测试'));
console.log('  Only English:', estimateTokenCount('hello world test'));
console.log('  Mixed with numbers:', estimateTokenCount('Hello 123 世界 456'));

console.log('\n✅ All utility tests completed successfully!');
